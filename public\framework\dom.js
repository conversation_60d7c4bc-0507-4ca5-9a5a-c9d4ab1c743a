export function createElement(nodeObj) {
  const el = document.createElement(nodeObj.tag);
  if (nodeObj.attrs) {
    for (let [key, value] of Object.entries(nodeObj.attrs)) {
      el.setAttribute(key, value);
    }
  }
  if (nodeObj.children) {
    nodeObj.children.forEach(child => {
      const childEl = typeof child === "string"
        ? document.createTextNode(child)
        : createElement(child);
      el.appendChild(childEl);
    });
  }
  return el;
}
