/* خلفية عامة */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #d7e7fe;
  margin: 0;
  padding: 2rem;
  display: flex;
  justify-content: center;
}

/* الحاوية الرئيسية */
#app {
  width: 100%;
  max-width: 600px;
  background-color: rgb(225, 240, 245);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
}

/* العنوان الكبير */
.title {
  text-align: center;
  font-size: 80px;
  font-weight: 200;
  color: #3f47b8;
  margin-bottom: 20px;
  user-select: none;
}

/* حقل الإدخال */
#todo-input {
  width: 100%;
  padding: 15px;
  font-size: 18px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
  box-sizing: border-box;
}

/* قائمة المهام */
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

li {
  display: flex;
  align-items: center;
  background-color: #ebedf0;
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 8px;
  border: 1px solid #6b74eb;
  transition: background-color 0.2s;
}

li:hover {
  background-color: #f0f0f0;
}

li.done {
  text-decoration: line-through;
  color: #aaa;
}

/* checkbox */
.todo-checkbox {
  margin-right: 10px;
  transform: scale(1.2);
}

/* النص القابل للتعديل */
.todo-text {
  flex: 1;
  margin-left: 10px;
  font-size: 18px;
  cursor: pointer;
}

/* زر الحذف */
.delete-btn {
  background: none;
  border: none;
  color: rgb(65, 51, 221);
  font-weight: bold;
  font-size: 18px;
  cursor: pointer;
}

.delete-btn:hover {
  color: rgb(0, 4, 255);
}

/* تعديل المهمة */
.edit-input {
  flex: 1;
  font-size: 18px;
  padding: 5px;
}

/* شريط الفلاتر والعداد */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 20px;
  gap: 10px;
}

#todo-count {
  font-size: 14px;
  color: #666;
}

/* أزرار الفلاتر */
.filter {
  padding: 6px 12px;
  border: 1px solid #ccc;
  background-color: white;
  border-radius: 5px;
  cursor: pointer;
}

.filter:hover {
  border-color: #999;
}

.filter.active {
  border-color: #4d3fb8;
  color: #4b3fb8;
  font-weight: bold;
}

/* زر مسح المهام المكتملة */
#clear-completed {
  padding: 6px 12px;
  border: none;
  background-color: transparent;
  color: #3f51b8;
  cursor: pointer;
}

#clear-completed:hover {
  text-decoration: underline;
}

.toggle-all {
  background: none;
  border: none;
  font-size: 22px;
  transform: rotate(90deg);
  margin: 10px 10px 0 10px;
  cursor: pointer;
  color: #1212e6;
}
