import {
  createElement,
  setState,
  getState,
  subscribe,
  on
} from "../framework/index.js";
import { addRoute, startRouter } from "../framework/router.js";

// الحالة الابتدائية
setState({
  todos: [],
  filter: "all"
});

const inputTodo = {
  tag: "input",
  attrs: {
    id: "todo-input",
    placeholder: "What needs to be done?",
    type: "text"
  }
};

function renderTodos(todos) {
  return {
    tag: "ul",
    attrs: { id: "todo-list" },
    children: todos.map((todo, index) => ({
      tag: "li",
      attrs: {
        "data-index": index,
        class: todo.done ? "done" : ""
      },
      children: [
        {
          tag: "input",
          attrs: {
            type: "checkbox",
            class: "todo-checkbox",
            ...(todo.done ? { checked: true } : {})
          }
        },
        {
          tag: "span",
          attrs: { class: "todo-text" },
          children: [todo.text]
        },
        {
          tag: "button",
          attrs: { class: "delete-btn" },
          children: ["X"]
        }
      ]
    }))
  };
}

function renderApp() {
  const app = document.getElementById("app");
  app.innerHTML = "";

  const heading = document.createElement("h1");
  heading.textContent = "todos";
  heading.className = "title";
  app.appendChild(heading);

  const state = getState();
  let todos = state.todos;

  if (state.filter === "active") {
    todos = todos.filter(t => !t.done);
  } else if (state.filter === "completed") {
    todos = todos.filter(t => t.done);
  }

  // زر Select All
  const selectAllButton = document.createElement("button");
  selectAllButton.id = "toggle-all";
  selectAllButton.textContent = "❯";
  selectAllButton.className = "toggle-all";

  const todoInput = createElement(inputTodo);
  const todoList = createElement(renderTodos(todos));

  app.appendChild(selectAllButton);
  app.appendChild(todoInput);
  app.appendChild(todoList);

  let filters = null;
  if (state.todos.length > 0) {
    filters = createElement({
      tag: "div",
      attrs: { class: "footer" },
      children: [
        {
          tag: "span",
          attrs: { id: "todo-count" },
          children: [`${state.todos.filter(t => !t.done).length} items left!`]
        },
        {
          tag: "button",
          attrs: {
            class: "filter",
            "data-filter": "all",
            onclick: "location.hash='all'"
          },
          children: ["All"]
        },
        {
          tag: "button",
          attrs: {
            class: "filter",
            "data-filter": "active",
            onclick: "location.hash='active'"
          },
          children: ["Active"]
        },
        {
          tag: "button",
          attrs: {
            class: "filter",
            "data-filter": "completed",
            onclick: "location.hash='completed'"
          },
          children: ["Completed"]
        },
        ...(state.todos.some(t => t.done)
          ? [{ tag: "button", attrs: { id: "clear-completed" }, children: ["Clear completed"] }]
          : [])
      ]
    });
  }

  if (filters) {
    app.appendChild(filters);

    // إبراز الفلتر المختار
    document.querySelectorAll(".filter").forEach(btn => {
      btn.classList.remove("active");
      if (btn.dataset.filter === state.filter) {
        btn.classList.add("active");
      }
    });
  }
}

//  ربط الفلاتر مع الراوتر
addRoute("", () => setState({ filter: "all" }));
addRoute("all", () => setState({ filter: "all" }));
addRoute("active", () => setState({ filter: "active" }));
addRoute("completed", () => setState({ filter: "completed" }));
startRouter();

subscribe(renderApp);
renderApp();

//  إضافة مهمة جديدة
on("keydown", "#todo-input", e => {
  if (e.key === "Enter") {
    const value = e.target.value.trim();
    if (value) {
      const state = getState();
      const newTodos = [...state.todos, { text: value, done: false }];
      setState({ todos: newTodos });
      e.target.value = "";
    }
  }
});

// تبديل حالة المهمة
on("click", ".todo-checkbox", e => {
  const index = e.target.closest("li").dataset.index;
  const state = getState();
  const newTodos = [...state.todos];
  newTodos[index].done = !newTodos[index].done;
  setState({ todos: newTodos });
});

// حذف مهمة
on("click", ".delete-btn", e => {
  const index = e.target.closest("li").dataset.index;
  const state = getState();
  const newTodos = state.todos.filter((_, i) => i != index);
  setState({ todos: newTodos });
});

//  مسح المهام المكتملة
on("click", "#clear-completed", () => {
  const state = getState();
  const newTodos = state.todos.filter(todo => !todo.done);
  setState({ todos: newTodos });
});

//  تعديل المهمة عند الضغط مرتين
on("dblclick", ".todo-text", e => {
  const span = e.target;
  const li = span.closest("li");
  const index = li.dataset.index;
  const originalText = span.textContent;

  const input = document.createElement("input");
  input.type = "text";
  input.value = originalText;
  input.className = "edit-input";
  span.replaceWith(input);
  input.focus();

  function saveEdit() {
    const newText = input.value.trim();
    if (newText && newText !== originalText) {
      const state = getState();
      const newTodos = [...state.todos];
      newTodos[index].text = newText;
      setState({ todos: newTodos });
    } else {
      input.replaceWith(span);
    }
  }

  input.addEventListener("blur", saveEdit);
  input.addEventListener("keydown", (e) => {
    if (e.key === "Enter") saveEdit();
  });
});

// ❯ زر تحديد الكل
on("click", "#toggle-all", () => {
  const state = getState();
  const allDone = state.todos.every(t => t.done);
  const newTodos = state.todos.map(t => ({ ...t, done: !allDone }));
  setState({ todos: newTodos });
});
