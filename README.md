#  MyMiniJS Framework

A lightweight JavaScript framework for DOM abstraction, state management, routing, and custom event handling — built entirely using Vanilla JS!

---

## Features

- Create DOM elements using `createElement`
- Reactive state management using `setState` and `getState`
- Custom event binding with `on`
- Simple hash-based routing with `addRoute`
- Automatic UI re-rendering on state change

---

## 🛠 Usage Examples

### Create an element

```js
createElement({
  tag: "h1",
  children: ["Hello World"]
});
```

### Add attributes

```js
createElement({
  tag: "button",
  attrs: { id: "myBtn", class: "btn" },
  children: ["Click"]
});
```

### Nest elements

```js
createElement({
  tag: "div",
  children: [
    { tag: "h1", children: ["Title"] },
    { tag: "p", children: ["Text here."] }
  ]
});
```

### Handle events

```js
on("click", "#myBtn", () => {
  alert("Button clicked!");
});
```

### State management

```js
setState({ count: 0 });
const state = getState();
console.log(state.count); // 0
```

### Routing

```js
addRoute("home", () => {
  console.log("Home route");
});
startRouter();
```

---

##  How the Framework Works

The framework is built around a simple reactive architecture:
- `createElement` creates and returns real DOM elements from virtual descriptions.
- `setState` updates global state and triggers UI re-render using `subscribe`.
- `on` binds DOM events using delegation for dynamic elements.
- `addRoute` and `startRouter` handle client-side routing using `location.hash`.

---

##  Getting Started

1. Use the `framework/` folder to build your app.
2. Build UI using `createElement`.
3. Handle events using `on`.
4. Manage app data using `setState` and `getState`.
5. Use hash-based routing with `addRoute` and `startRouter`.

---

##  Example TodoMVC

Check the `todomvc/` folder for a fully working demo of a to-do list app using the framework.

---

##  Comparison with Official TodoMVC

| Feature                     | MyMiniJS TodoMVC| Official TodoMVC 
|-----------------------------|-----------------|------------------
| Add todos                   | yes             | yes          
| Mark as completed           | yes             |    yes             
| Edit on double click        | yes             | yes              
| Select All toggle           | yes             | yes              
| Routing (All/Active/Completed) | yes          | yes              
| Custom framework            | yes (Vanilla JS) | NO(uses React/Angular/etc.) 



##  Screenshot

 ![alt text](image.png)



## Final Notes

This project was created as part of a learning journey to understand front-end frameworks from scratch using only Vanilla JavaScript.


Thanks for reading 